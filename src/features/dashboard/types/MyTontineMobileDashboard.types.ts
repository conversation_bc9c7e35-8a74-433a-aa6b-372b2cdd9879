import type {
  InvestmentStrategyId,
  SexType,
  TontinatorParamsMode,
} from '../../../common/types/CommonTypes.types'
import type { AgeMonth } from '../../CommonState.type'
// Type definitions for MyTontineMobileDashboard component
import type {
  InvestmentDetails,
  UserDetails,
} from '../../authentication/types/AuthMachineTypes.type'

// Re-export UserDetails from auth types
export type { UserDetails }

// Hook return types for useLocalization
export type DetectedCountry = {
  alpha3?: string
}

export type FormatAmountParams = {
  amount: number | bigint
  currency?: string
  style?: 'percent' | 'currency'
  notation?: 'standard' | 'engineering' | 'compact' | 'scientific'
  digits?: {
    minimumFractionDigits?: number
    maximumFractionDigits?: number
    maximumSignificantDigits?: number
    minimumSignificantDigits?: number
  }
}

export type FormatAmountReturn =
  | {
      formattedAmountWithSymbol: string
      symbol: string
      formattedAmount: string
    }
  | undefined

// Hook return types for useSupportedCountries - matching actual TontinatorUIParams
export type TontinatorUIParams = {
  defaultRetirementAgeSlider?: AgeMonth
  defaultCurrentAgeSlider?: AgeMonth
  defaultOneTimeSliderValue?: number
  defaultMonthlySliderValue?: number
  defaultSex?: SexType
  oneTimeContribution?: number[]
  monthlyContribution?: number[]
  oneTimeContributionIfRetired?: number[]
  monthlyContributionMinIfOnly?: number
  oneTimeContributionMinIfOnly?: number
  minRetirementAge?: AgeMonth
  maxRetirementAge?: AgeMonth
  minCurrentAge?: AgeMonth
  maxCurrentAge?: AgeMonth
}

// Re-export InvestmentDetails from auth types
export type { InvestmentDetails }

export type SupportedCountry = {
  alpha3?: string
  supportedInvestments?: InvestmentDetails
  tontinatorParams?: TontinatorUIParams
}

// Hook return types for useBankingService
export type BankContext = {
  bankingInfo?: {
    nextPayout?: Array<{
      gross?: {
        amount?: number
        currency?: string
      }
    }>
    payinHistory?: Array<{
      nominalBalance: {
        amount: number
        currency: string
      }
    }>
  }
  bankingInfoError?: unknown
}

// Return type for chooseDefaultParams function - matching actual return type
export type DefaultParams = {
  contributionAge?: AgeMonth
  retirementAge?: AgeMonth
  sex?: SexType
  oneTimeContribution?: number
  countryOfResidence?: string
  monthlyContribution?: number
  strategy?: InvestmentStrategyId
  paramsMode?: TontinatorParamsMode
}

// Component prop types
export type NextPayoutCardProps = {
  className?: string
}

export type FundedDashboardProps = {
  data?: DefaultParams
  error?: null
  isLoading?: boolean
}

export type MobileAppBarProps = {
  completedKyc?: boolean
}

export type NavigationCardProps = {
  headerImage?: string
  subTitle?: string
  title?: string
  navigateTo?: string
  showArrow?: boolean
  variant?: string
}

export type HeaderProps = {
  className?: string
  title?: string
}

export type PageLayoutProps = {
  containerHeight?: string
  containerMt?: string
  children?: React.ReactNode
}
