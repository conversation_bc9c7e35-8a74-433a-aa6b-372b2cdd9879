import { lazy } from 'react'
import TextError from '../../../common/components/TextError'
import { ANIMATION } from '../../../common/constants/Animations'
import { CONSTANTS } from '../../../common/constants/ConstantValues'
import { useLocalization } from '../../../common/hooks/useLocalization'
import { useTranslate } from '../../../common/hooks/useTranslate'
import NoTransactions from '../../banking/components/NoTransactions'
import {
  BankMachineContext,
  States,
} from '../../banking/types/BankMachineTypes.type'
import { useGraphResize } from '../../visualization/hooks/useGraphResize'
import { generateLegendItem } from '../../visualization/utils/D3DataUtils'
import style from '../style/NominalBalanceDashboard.module.scss'
const BalanceLineChart = lazy(
  () => import('../../../features/visualization/chart/BalanceLineChart')
)

const X_AXIS_CLASS = 'x-axis-cnb-months'
const Y_AXIS_CLASS = 'y-axis-cnb-nominal'
//Contributions
const CONTRIBUTIONS_LINE_KEY = 'contributions'

//Nominal balance
const NOMINAL_BALANCE_LINE_KEY = 'nominal-balance-line'

const SVG_CONTAINER_ID = 'nominal-balance-svg-container'
const CONTAINER_CLASS = 'nominal-balance-container'

const contributionsStyling = {
  // Greenish
  color: '#22B573',
  stroke: '3px',
  fill: 'none',
  areaOpacity: 0.2,
  areaColor: '#22B573',
  dashArray: '',
  showAnnotations: true,
  arrowHead: false,
  inflationAreaOpacity: 0.1,
  transitionDuration: 300,
}
const nominalBalanceStyling = {
  // Yellow greenish
  color: '#C9D915',
  stroke: '3px',
  fill: 'none',
  areaOpacity: 0.2,
  areaColor: '#C9D915',
  dashArray: '',
  showAnnotations: true,
  arrowHead: false,
  inflationAreaOpacity: 0.1,
  transitionDuration: 300,
}

/**
 * Renders the Current Nominal balance graph with a chart lines
 * legend above it. This component also listens for a `resize` event to redraw
 * the graph if the `window` dimensions have changed
 */
const NominalBalanceDashboard = ({
  bankContext,
  currentBankState,
  states,
}: {
  bankContext?: BankMachineContext
  currentBankState?: keyof States
  states?: States
}) => {
  const draw = useGraphResize()
  const t = useTranslate()
  const { formatAmount } = useLocalization()

  if (
    bankContext?.bankingInfo?.nominalBalance &&
    bankContext.bankingInfo.nominalBalance?.length === 0
  ) {
    return (
      <NoTransactions>
        {t('BANKING.CONTRIBUTIONS_HISTORY_PAGE_CONTENT')}
      </NoTransactions>
    )
  }

  return (
    <section className={style['nominalBalanceDashboard']}>
      {bankContext?.bankingInfoError && (
        <TextError
          errorText={
            bankContext?.bankingInfoError?.translatedError ||
            'An error occurred'
          }
          className="tontinatorDashboard__error-text"
        />
      )}
      <BalanceLineChart
        formatter={(amount) => {
          if (
            typeof amount === 'number' &&
            bankContext?.bankingInfo?.nominalBalance?.[0]?.nominalBalance
              ?.currency
          ) {
            const formatted = formatAmount({
              amount,
              currency:
                bankContext?.bankingInfo?.nominalBalance?.[0]?.nominalBalance
                  ?.currency,
              style: 'currency',
              notation: 'compact',
            })
            return formatted?.formattedAmountWithSymbol || amount.toString()
          }
          return amount.toString()
        }}
        //Sets the width and height of the graph
        //initially to follow the window's innerHeight and innerWidth
        //which means the graph will be 100% width of it's container
        showLegend
        data={
          bankContext?.bankingInfo?.nominalBalance?.filter(
            (balance) =>
              balance?.transaction?.["type'"] === CONSTANTS.contribution
          ) || []
        }
        height={innerHeight}
        width={innerWidth}
        redraw={draw}
        isLoading={currentBankState === states?.FETCHING_BANK_INFO}
        drawingAnimation={ANIMATION.jarWithCoins}
        containerCssClass={CONTAINER_CLASS}
        contributionsStyleOptions={contributionsStyling}
        nominalBalanceStyling={nominalBalanceStyling}
        numOfTicksForX={
          bankContext?.bankingInfo?.nominalBalance?.length &&
          bankContext?.bankingInfo?.nominalBalance?.length < 6
            ? bankContext?.bankingInfo?.nominalBalance?.length
            : 6
        }
        numOfTicksForY={5}
        axisDistanceFromGraph={5}
        mainSVGContainerID={SVG_CONTAINER_ID}
        xAxisCssClass={X_AXIS_CLASS}
        yAxisCssClass={Y_AXIS_CLASS}
        contributionLineKey={CONTRIBUTIONS_LINE_KEY}
        nominalBalanceLineKey={NOMINAL_BALANCE_LINE_KEY}
        legendData={[
          generateLegendItem({
            text: t('DASHBOARD.CHART_SUBTITLE_CNB'),
            itemColor: nominalBalanceStyling.color,
            renderLine: true,
            id: 'nominal-balance-legend',
          }),
          generateLegendItem({
            text: t('DASHBOARD.CURRENT_BALANCE_CHART_LEGEND'),
            itemColor: contributionsStyling.color,
            renderLine: true,
            id: 'contributions-legend',
          }),
        ]}
      />
    </section>
  )
}

export default NominalBalanceDashboard
