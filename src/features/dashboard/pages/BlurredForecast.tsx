import Modal from '../../../common/components/Modal'
import { parseParamsForEmail } from '../../../common/utils/UtilFunctions'
import RegisterFormModal from '../../authentication/pages/RegisterFormModal'
import TontineDashboardLayout from '../components/TontineDashboardLayout'
import SandboxTontinatorPage from './SandboxTontinatorPage'

import style from '../../../common/style/BlurredForecastModal.module.scss'

/**
 * Renders a blurred Tontinator page
 */
const BlurredForecast = ({
  incomeForecastParams,
  isAuthenticated,
}: {
  incomeForecastParams?: import(
    '../../../common/types/CommonTypes.types'
  ).IncomeForecastParams
  isAuthenticated?: boolean
}) => {
  return (
    <>
      {!isAuthenticated && (
        <Modal
          isOpen
          backdrop
          className="blurredForecastModal"
          customStyle={style}
        >
          <RegisterFormModal
            forecastPageRegisterModal
            forecastUserData={
              incomeForecastParams
                ? parseParamsForEmail(incomeForecastParams)
                : undefined
            }
          />
        </Modal>
      )}
      <div
        style={{
          // Blurs the forecast page for external users
          filter: 'blur(5px)',
        }}
      >
        <TontineDashboardLayout>
          <SandboxTontinatorPage />
        </TontineDashboardLayout>
      </div>
    </>
  )
}

export default BlurredForecast
