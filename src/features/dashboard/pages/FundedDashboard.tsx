import Button from '../../../common/components/Button'
import DividerHeader from '../../../common/components/DividerHeader'
import ErrorBoundaryAndSuspense from '../../../common/components/ErrorBoundaryAndSuspense'
import Icon from '../../../common/components/Icon'
import LottieAnimation from '../../../common/components/LottieAnimation'
import TextError from '../../../common/components/TextError'
import { ANIMATION } from '../../../common/constants/Animations'
import { ASSET } from '../../../common/constants/Assets'
import { useCustomNavigation } from '../../../common/hooks/useCustomNavigation'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { DASHBOARD_NAVIGATION } from '../../../routes/Route'
import { useAccountService } from '../../authentication/hooks/useAccountService'
import NextPayoutCard from '../components/NextPayoutCard'
import PensionPlanDashboard from '../components/PensionPlanDashboard'
import style from '../style/ProgressDashboard.module.scss'

import { IncomeForecastParams } from '../../../common/types/CommonTypes.types'
/**
 * @wip Until design is done and the feature is well defined
 * Renders pension plan lines as views, DOES NOT MODIFY OR SAVE DATA
 */
import { ErrorStorage } from '../../../features/CommonState.type'

const FundedDashboard = ({
  data,
  error,
  isLoading,
}: {
  data?: {
    plan_to_draw?: {
      data?: unknown
      plan_id?: string
    }
    draft_plan?: {
      plan_id?: string
    }
  }
  error?: ErrorStorage
  isLoading?: boolean
}) => {
  const t = useTranslate()
  const navigate = useCustomNavigation()
  const {
    context: { user_details },
  } = useAccountService()

  const isPlan =
    Boolean(data?.plan_to_draw?.data) || Boolean(data?.plan_to_draw?.plan_id)

  if (isLoading) {
    return (
      <LottieAnimation
        loop
        autoplay
        animationName={ANIMATION.loadingLightBlueDots}
      />
    )
  }

  return (
    <>
      {user_details?.kyc_status?.L2?.passed_level && (
        <NextPayoutCard className={style[`progress-dashboard__next-payout`]} />
      )}

      <DividerHeader
        headerText={t('DASHBOARD.CHART.TITLE.EXPECTED.MONTHLY.INCOME')}
      />
      {error && <TextError errorText={error.translatedError} />}

      {!isLoading && !isPlan && (
        <>
          <div className={style[`progress-dashboard__animation-container`]}>
            <Icon
              fileName={ASSET.dashboardPlaceholder}
              className={style[`progress-dashboard__animation`]}
            />
          </div>
          <section className={style[`progress-dashboard__button-container`]}>
            <Button
              className={style[`progress-dashboard__button`]}
              variant="primary"
              onClick={() => navigate(DASHBOARD_NAVIGATION.TONTINATOR)}
            >
              {t('CRATE_PLAN_BUTTON')}
            </Button>
          </section>
        </>
      )}

      {!isLoading && isPlan && (
        <ErrorBoundaryAndSuspense>
          <PensionPlanDashboard
            dataToDraw={[
              {
                countryOfResidence: user_details?.residency ?? '',
                contributionAge: { age: 0, month: 0 },
                sex: 'Male',
                monthlyContribution: 0,
                oneTimeContribution: 0,
                strategy: 'BOL',
              } as IncomeForecastParams,
            ]}
            className={style[`progress-dashboard__chart`]}
          />
        </ErrorBoundaryAndSuspense>
      )}
    </>
  )
}

export default FundedDashboard
