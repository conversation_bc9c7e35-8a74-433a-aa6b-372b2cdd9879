import Button from '../../../common/components/Button'
import NavigationButtons from '../../../common/components/NavigationButtons'
import { UI_TEST_ID } from '../../../common/constants/DataTestIDs'
import { useCustomNavigation } from '../../../common/hooks/useCustomNavigation'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { ACCOUNT_MENU, PRIVATE } from '../../../routes/Route'
import ReactivateAccountButton from './ReactivateAccountButton'

/**
 * Renders different types of buttons depending on the state of the user's
 * closing account progress
 */
const CloseAccountButtons = ({
  reactivatedAccount,
  confirmClosing,
  setConfirmClosing,
  scheduledClosingTime,
  setReactivatedAccount,
  onClickCloseAccount,
}: {
  reactivatedAccount?: boolean
  confirmClosing?: boolean
  setConfirmClosing?: (confirmClosing: boolean) => void
  scheduledClosingTime?: unknown
  setReactivatedAccount?: (reactivated: boolean) => void
  onClickCloseAccount?: () => void
}) => {
  const t = useTranslate()
  const navigate = useCustomNavigation()

  return (
    <div>
      {!reactivatedAccount && (
        <NavigationButtons
          onClickFirst={() => {
            if (!confirmClosing) {
              navigate(ACCOUNT_MENU.SETTINGS)
              return
            }
            setConfirmClosing?.(false)
          }}
          hideActionButton
          customButton={
            scheduledClosingTime ? (
              <ReactivateAccountButton
                reactivateAccountButtonLabel={t(
                  'CLOSE.ACCOUNT.REACTIVATE_ACCOUNT_BTN'
                )}
                onAccountReactivated={setReactivatedAccount}
                dataTestID={UI_TEST_ID.reactivateAccountButton}
              />
            ) : (
              <Button
                variant="secondary"
                onClick={onClickCloseAccount}
                dataTestID={UI_TEST_ID.closeAccountInitial}
              >
                {t('CLOSE_ACCOUNT.CLOSE_BTN')}
              </Button>
            )
          }
        />
      )}

      {reactivatedAccount && (
        <NavigationButtons
          hideBackButton={reactivatedAccount}
          hideActionButton
          customButton={
            <Button
              onClick={() => navigate(PRIVATE.MYTT_DASHBOARD)}
              dataTestID={UI_TEST_ID.checkPlansButton}
            >
              {t('DASHBOARD.CHART_BUTTON_GO_BACK_HOMEPAGE')}
            </Button>
          }
        />
      )}
    </div>
  )
}

export default CloseAccountButtons
