import dayjs from 'dayjs'
import { t } from 'i18next'
import { CONSTANTS } from '../../../common/constants/ConstantValues'
import { CardAlertType } from '../../../common/types/Card.types'
import {
  IncomeForecastRequestBody,
  StateData,
} from '../../../common/types/CommonTypes.types'
import {
  captureExceptionWithSentry,
  formatFileSize,
  i18Translation,
  secondsToMinutes,
} from '../../../common/utils/UtilFunctions'
import {
  ResidencyDeniedReason,
  ResidencyReviewStatus,
  SubmissionStatus,
  UserDetails,
} from '../types/AuthMachineTypes.type'
import { SaveChoiceAndChangeStateParams } from '../types/DateOfBirth.type'
import { SignUpFieldsType } from '../types/SignUpFields.types'
import {
  type allowedFileTypesForAddressVerification,
  appDomains,
} from './consts'

const CURRENT_YEAR = dayjs().year()

/**
 * Called when the close modal button is clicked. It saves a variable in local
 * storage to check if the modal was shown to the user once
 */
const saveChoiceAndChangeState = ({
  onClickCloseButton,
  saveInLocalStorage,
}: SaveChoiceAndChangeStateParams) => {
  onClickCloseButton?.()
  saveInLocalStorage?.(true)
}

/** Converts milliseconds to seconds, rounding down to the nearest whole number.
 * Returns `null` if the input is not a positive number. */
const millisecondsToSeconds = (milliseconds: number) =>
  milliseconds > 0 ? Math.floor(milliseconds / 1_000) : undefined

/** For provided time in seconds, converts the seconds into minute
 * and remaining seconds and returns an object `{minutes, remainingSeconds}` */
const toMinutesAndRemainingSeconds = (seconds?: number) => {
  if (seconds && seconds > 0) {
    const minutes = secondsToMinutes(seconds)
    // Checks for null since the function returns number or null
    if (minutes === null) {
      throw Error(`Invalid minutes calculation for seconds: ${seconds}`)
    }
    const remainingSeconds = seconds - minutes * 60
    return { minutes, remainingSeconds }
  }
  throw Error(
    `Provided argument: ${seconds} is negative number or not a number`
  )
}

/** Returns the appropriate status text explanting the status to the user */
const idVerificationStatusText = (status?: SubmissionStatus) => {
  const statusTextMap = {
    approved: i18Translation('ID_VERIFY_VERIFIED'),
    not_reviewed: i18Translation(
      'AUTH.CARD_ID_VERIFICATION_STATUS_BEING_VERIFIED'
    ),
    rejected: i18Translation('ID_VERIFY_REJECTED'),
  }

  return status ? statusTextMap[status] : i18Translation('ID_VERIFY_SUBTITLE')
}

/**
 * Calculates the birth YYYY for an user that has provided their `currentAge`,
 * current age and contribution age are the same, just they are named
 * differently since contribution age comes from onboarding flow and current age
 * comes from sign up form. It was left like this for legacy reasons
 */
const calculateUserBirthYear = (userCurrentAge: number) => {
  try {
    if (userCurrentAge > 0) {
      return CURRENT_YEAR - userCurrentAge
    }
    throw new Error(
      `Got invalid params >>${userCurrentAge}<< should be a number or not null`
    )
  } catch (error) {
    console.error(error)
  }
  return -1
}

/**
 * Forms a unified request body from the forecast params and the data from the
 * sign up fields. This is needed in order to fill the data that has been
 * obtained only from the tontinator where we do not explicitly ask it form the
 * user.
 */
const parseRegisterBody = (
  forecastParams?: IncomeForecastRequestBody,
  signUpData?: SignUpFieldsType
) => {
  try {
    let forecastSex = undefined
    let forecastCurrentAgeMonth = undefined
    let forecastCountryOfResidence = undefined
    let forecastRetirementAge = undefined
    let payoutStartYear = undefined

    //If the forecast params are passed prioritize them
    if (forecastParams) {
      const {
        demographic_data_current_age,
        demographic_data_country_of_residence: country_of_residence,
        demographic_data_sex: sex,
        contributions: {
          payout_age: { age: retirementAge },
        },
      } = forecastParams

      forecastSex = sex
      forecastCurrentAgeMonth = demographic_data_current_age
      forecastCountryOfResidence = country_of_residence
      forecastRetirementAge = retirementAge
    }

    // Checks if the retirement age is present in the forscast
    if (forecastRetirementAge && forecastCurrentAgeMonth) {
      // Calculate the payout start year
      payoutStartYear =
        CURRENT_YEAR + (forecastRetirementAge - forecastCurrentAgeMonth.age)
    }

    const {
      email,
      currentAge,
      firstName,
      lastName,
      emailUpdates,
      sex,
      country,
      referralCode,
      termsVersion,
    } = signUpData ?? ({} as SignUpFieldsType)

    //Check for any undefined data
    if (
      !email ||
      (currentAge && currentAge < 0) ||
      !firstName ||
      !lastName ||
      !sex ||
      !country ||
      termsVersion === undefined ||
      termsVersion === null
    ) {
      throw new Error(
        `Invalid mandatory Sign up params got
         >>${email}<<
         >>${currentAge}<<
         >>${firstName}<<
         >>${lastName}<<
         >>${sex}<<
         >>${country}<<
         >>${termsVersion}<<`
      )
    }

    //0 just so javascript can type this as a number
    let birth_year = 0

    //If forecast year exists then use that
    if (forecastCurrentAgeMonth) {
      birth_year = calculateUserBirthYear(forecastCurrentAgeMonth.age ?? 0)
    } else {
      //Sign up params current_age is a number here
      birth_year = calculateUserBirthYear(currentAge ?? 0)
    }

    let referralCodeToTier = undefined

    if (referralCode) {
      referralCodeToTier = `${CONSTANTS.REFERRAL_CODE_PREFIX}${referralCode}`
    }

    return {
      email: email?.trim()?.toLowerCase(),
      first_name: firstName?.trim(),
      last_name: lastName?.trim(),
      email_updates: emailUpdates,
      terms_and_conditions: termsVersion,
      birth_year,
      sex: forecastSex ?? sex,
      residency: forecastCountryOfResidence ?? country,
      referral_code: referralCodeToTier,
      payout_start_year: payoutStartYear,
    }
  } catch (error) {
    console.error(
      `Error occurred, while parsing register params error: ${error as string}`
    )
    captureExceptionWithSentry(
      new Error(`Error occurred API /create: ${JSON.stringify(error)}`)
    )
  }

  return undefined
}

/** Separates all possible reasons */
const idRejectionReason = (reasonsArray?: Array<string>) => {
  const possibleReasons = {
    possibly_altered: i18Translation('REJECTION_REASON.POSSIBLE_ALTERED_ID'),
    image_not_clear: i18Translation('REJECTION_REASON.IMAGE_NOT_CLEAR'),
    user_confirmed_fields_mismatch: i18Translation(
      'REJECTION_REASON.USER_CONFIRMED_FIELDS_MISMATCH'
    ),
  }
  if (reasonsArray && reasonsArray.length > 0) {
    return reasonsArray.map((reason) => {
      if (reason in possibleReasons) {
        return possibleReasons[reason as keyof typeof possibleReasons]
      }
      return ''
    })
  }
  return []
}

/** Calculates the number how many KYC alerts need to be rendered */
const kycAlertCount = (user_details?: UserDetails) => {
  //Initially all flow alerts are set to 0
  let kycAlerts = {
    personalDetails: 0,
    proofOfResidency: 0,
  }

  try {
    if (user_details) {
      //If a user has not completed a KYC for a certain flow, increment the KYC
      //alert count
      if (user_details?.id_review_status === null) {
        kycAlerts = {
          ...kycAlerts,
          personalDetails: kycAlerts.personalDetails + 1,
        }
      }

      if (!user_details?.kyc_status?.L2?.requirements?.address_verified) {
        kycAlerts = {
          ...kycAlerts,
          proofOfResidency: kycAlerts.proofOfResidency + 1,
        }
      }

      return kycAlerts
    }
    throw new Error(
      `Value kycAlertCount expected argument got >>>${user_details}<<<`
    )
  } catch (error) {
    console.error(error)
  }
  return
}

/** Validates whether the provided pin is complete and has the correct length.
 */
const validatePin = (pin: Array<string>, pinLength: number) => {
  return pin.length === pinLength && pin.every((pin) => pin)
}

/**
 * Exclude the domain from a referral link and return the referral code without the domain
 */
const excludeDomainFromUrl = (referralLink: string) => {
  if (referralLink) {
    // destruct the referral link into chunks
    const [domain] = referralLink.split('/u')
    // construct the url back with protocol domain and the referral prefix
    const constructedUrlWithPrefix = `${domain}/${CONSTANTS.REFERRAL_CODE_PREFIX}`
    if (appDomains.includes(domain)) {
      // return the referral link without domain, prefix only the referral code
      return referralLink.replaceAll(constructedUrlWithPrefix, '')
    }
  }

  return referralLink
}

/**
 * Checks file size and file type if they are valid, and returns an object with
 * valid boolean and error message
 */
const isAttachedFileValid = ({
  file,
  acceptedFileTypes,
  sizeLimit,
}: {
  file: File
  acceptedFileTypes: typeof allowedFileTypesForAddressVerification
  sizeLimit: number
}) => {
  if (
    !acceptedFileTypes.includes(file.type as (typeof acceptedFileTypes)[number])
  ) {
    return {
      valid: false,
      message: t('ERROR_INVALID_FILE_TYPE', {
        acceptedFileTypes: acceptedFileTypes.reduce(
          (acc, fileType) => `${acc}, ${fileType}`,
          ''
        ),
      }),
    }
  }

  if (file.size > sizeLimit) {
    return {
      valid: false,
      message: t('ERROR_FILE_TOO_LARGE', {
        fileSizeLimit: formatFileSize(sizeLimit),
      }),
    }
  }

  return {
    valid: true,
    message: undefined,
  }
}

/**
 * For passed in residency review status, returns an object with key and alert
 */
const statusToKey = (status?: ResidencyReviewStatus) => {
  const statuses: Record<
    ResidencyReviewStatus,
    { key: string; alert: CardAlertType }
  > = {
    APPROVED: { key: 'ADDRESS_VERIFICATION.VERIFIED', alert: 'completed' },
    UPLOADED: { key: 'ADDRESS_VERIFICATION.UPLOADED', alert: 'warn' },
    SUBMITTED: {
      key: 'ADDRESS_VERIFICATION.IN_ANALYSIS',
      alert: 'pending',
    },
    DENIED: { key: 'ADDRESS_VERIFICATION.DENIED', alert: 'error' },
  }

  if (status) {
    return statuses[status]
  }

  return {
    key: 'ADDRESS_VERIFICATION.NOT_UPLOADED',
    alert: 'error' as CardAlertType,
  }
}

/**
 * For passed in residency denied reason, returns the translation key
 */
const deniedAddressReasonType = (reason: ResidencyDeniedReason) => {
  const reasons: Record<ResidencyDeniedReason, string> = {
    NAME_MISMATCH: 'REJECTION_REASON.NAME_MISMATCH',
    DOCUMENT_TOO_OLD: 'REJECTION_REASON.DOCUMENT_TOO_OLD',
    ADDRESS_NOT_SHOWN: 'REJECTION_REASON.ADDRESS_NOT_SHOWN',
    ILLEGIBLE_DOCUMENT: 'REJECTION_REASON.ILLEGIBLE_DOCUMENT',
    OTHER_REASON: 'REJECTION_REASON.OTHER_REASON',
  }

  return reasons[reason]
}

/**
 * Handles state option for address info, if state object is provided, returns
 * the iso code, if a string is provided simply returns the string
 */
const handleStateOption = (stateOption: StateData | string) => {
  if (typeof stateOption === 'object' && 'iso_code' in stateOption) {
    return stateOption.iso_code
  }

  return stateOption ?? ''
}

export {
  deniedAddressReasonType,
  excludeDomainFromUrl,
  handleStateOption,
  idRejectionReason,
  idVerificationStatusText,
  isAttachedFileValid,
  kycAlertCount,
  millisecondsToSeconds,
  parseRegisterBody,
  saveChoiceAndChangeState,
  statusToKey,
  toMinutesAndRemainingSeconds,
  validatePin,
}
