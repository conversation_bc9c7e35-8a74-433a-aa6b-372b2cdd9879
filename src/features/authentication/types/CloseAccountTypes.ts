import { Dispatch, SetStateAction } from 'react'
import { SelectionItem } from '../../../common/types/MultiSelection.types'

// Feedback option interface for the multi-selection component
interface FeedbackOption extends SelectionItem {
  id: number
  title: string
}

// State interface for the multi-selection component data
interface BeforeClosingAccountFeedback {
  activeSelection: SelectionItem | undefined
  data: Array<FeedbackOption>
}

// Props interface for CloseAccountButtons component
interface CloseAccountButtonsProps {
  confirmClosing: boolean
  reactivatedAccount: boolean
  setConfirmClosing: Dispatch<SetStateAction<boolean>>
  scheduledClosingTime: string | undefined
  setReactivatedAccount: Dispatch<SetStateAction<boolean>>
  onClickCloseAccount: () => void
}

// Props interface for PIN authorization callback functions
interface PinAuthorizationCallbacks {
  onSuccessfulAccountScheduling: () => void
  onFailedAccountScheduling: (error: { translatedError: string }) => void
}

// Props interface for PIN change handler
interface PinChangeHandlerProps {
  pin: Array<string>
}

// Props interface for PIN submit handler
interface PinSubmitHandlerProps {
  pin: string
  payload: {
    closureFeedback: string
  }
}

// Props interface for useSubmitPin hook configuration
interface UseSubmitPinConfig {
  authMachineEvent: 'CLOSE_USER_ACCOUNT'
  successCallback: () => void
  failureCallback?: () => void
}

// Return type for useSubmitPin hook
interface UseSubmitPinReturn {
  setPin: Dispatch<SetStateAction<Array<string>>>
  pin: Array<string>
  handleSubmitPin: (props: PinSubmitHandlerProps) => void
}

export type {
  BeforeClosingAccountFeedback,
  CloseAccountButtonsProps,
  FeedbackOption,
  PinAuthorizationCallbacks,
  PinChangeHandlerProps,
  PinSubmitHandlerProps,
  UseSubmitPinConfig,
  UseSubmitPinReturn,
}
