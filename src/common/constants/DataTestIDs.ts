import { TESTING_IDS } from '../../../cypress/support/ui-component-ids'
import { envs } from '../../config/envs'
import { ENVIRONMENTS } from './ConstantValues'

const { environment } = envs

const { production, staging } = ENVIRONMENTS

/**
 * @note **Use these ONLY in UI elements to assign test IDs!**
 */
const UI_TEST_ID =
  //If app is using production or staging config then do not include testing
  //custom testing attributes that are added in components for security reasons
  environment === production || environment === staging
    ? ({} as typeof TESTING_IDS)
    : TESTING_IDS

export { UI_TEST_ID }
