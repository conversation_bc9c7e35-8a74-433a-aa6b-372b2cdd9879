import { CONSTANTS } from '../constants/ConstantValues'
import style from '../style/DateSliderBox.module.scss'
import { DateSliderProps } from '../types/DateSlider.types'
import { calculateRetirementValues } from '../utils/UtilFunctions'
import InputLabel from './InputLabel'
import MonthAndYearBubble from './MonthAndYearBubble'
import Range from './Range'
import DateSliderBox from './date-box/DateSliderBox'

const { DECEMBER, JANUARY } = CONSTANTS

/**
 * Date slider that renders a range input to select retirement year
 * and header component where you can adjust the years or months. Adjusting the
 * months does not affect the range slider.
 */
const DateSlider = ({
  sliderSteps,
  yearHeadLabel,
  monthHeadLabel,
  value,
  setValue,
  locale,
  yearsOldOnRetirementDateLabel,
  yearsOldOnRetirementDate,
  monthsOldOnRetirementDateLabel,
  monthsOldOnRetirementDate,
  label,
  caption,
  userDetails,
  ageThresholds,
}: DateSliderProps) => {
  //Maximum AGE and YEAR thresholds
  const { age: maxThresholdYearsOld, month: maxThresholdMonthsOld } =
    ageThresholds.maxRetirementAge

  const { year: maxThresholdYear, month: maxThresholdMonth } =
    ageThresholds.maxRetirementAgeYearMonth

  //Minimum AGE and YEAR thresholds
  const { age: minThresholdYearOld, month: minThresholdMonthsOld } =
    ageThresholds.minRetirementAge

  const { year: minThresholdYear, month: minThresholdMonth } =
    ageThresholds.minRetirementAgeYearMonth

  //Disables the UI increment MONTH chevrons
  const maxLimitsReached = checkMaxLimits(
    value?.year,
    maxThresholdYear,
    value?.month,
    maxThresholdMonth
  )

  //Disables the UI decrement MONTH chevrons
  const minLimitsReached = checkMinLimits(
    value?.year,
    minThresholdYear,
    value?.month,
    minThresholdMonth
  )

  /**
   * Does not allow the months to go out of range, always returns a value
   * between 1 and 12
   */
  const adjustMonth = (month: number) => ((month + 11) % DECEMBER) + 1

  /**
   * Does not allow the year to go bellow the `sliderSteps` first step, because
   * the first step is the minimum year the user can retire
   */
  const adjustYear = (year: number, month: number) => {
    // Ensure that year does not go beyond the maximum slider step value
    const maxYear = sliderSteps[sliderSteps.length - 1]
    let adjustedYear = year
    if (year > maxYear) {
      adjustedYear = maxYear
    }

    // Ensure that year does not go below the minimum slider step value
    const minYear = sliderSteps[0]
    if (year < minYear) {
      adjustedYear = minYear
    }
    //If user keeps clicking on the increment month chevron, and reaches january
    //then update the year +1
    if (month === JANUARY) {
      adjustedYear += 1
    }

    return adjustedYear
  }

  /**
   * Increment month value by one calling the adjustMonth function to adjust the
   * value and then updating the month in the state
   */
  const handleMonthsIncrementByOne = () => {
    const adjustedMonth = adjustMonth((value?.month || 0) + 1)
    const adjustedYear = adjustYear(value?.year || 0, adjustedMonth)

    const { age: yearsOldOnRetirementDate, months: monthsOldOnRetirementDate } =
      calculateRetirementValues(userDetails, {
        year: adjustedYear,
        month: adjustedMonth,
      }) || { age: 0, months: 0 }

    const newValue = {
      ...value,
      month: adjustedMonth,
      year: adjustedYear,
      yearsOld: yearsOldOnRetirementDate,
      monthsOld: monthsOldOnRetirementDate,
      retirementAge: {
        age: yearsOldOnRetirementDate,
        month: monthsOldOnRetirementDate,
      },
    }

    setValue(newValue)
  }

  /**
   * Decrement month value by one calling the adjustMonth function to adjust the
   * value and then updating the month in the state
   */
  const handleMonthsDecrementByOne = () => {
    const adjustedMonth = adjustMonth((value?.month || 0) - 1)
    let adjustedYear = value?.year || 0

    // Decrease the year by 1 if the adjusted month is December
    if (adjustedMonth === DECEMBER) {
      adjustedYear -= 1
    }

    const { age: ageOnYear, months: monthsOnYear } = calculateRetirementValues(
      userDetails,
      {
        year: adjustedYear,
        month: adjustedMonth,
      }
    ) || { age: 0, months: 0 }

    const newValue = {
      ...value,
      month: adjustedMonth,
      year: adjustedYear,
      yearsOld: ageOnYear,
      monthsOld: monthsOnYear,
      retirementAge: {
        age: ageOnYear,
        month: monthsOnYear,
      },
    }

    setValue(newValue)
  }

  /**
   * Increments of the year by one, by using a setState function
   */
  const handleYearsIncrement = () => {
    let newValue = { ...value }

    let year = value?.year || 0

    const maxLimitsReachedHere = checkMaxLimits(
      //We need to use the "next" year in order to prevent
      //the year going out of threshold limits, before it is committed
      //to the state
      year + 1,
      maxThresholdYear,
      value?.month,
      maxThresholdMonth
    )

    if (maxLimitsReachedHere) {
      newValue = {
        ...value,
        year: maxThresholdYear,
        month: maxThresholdMonth,
        yearsOld: maxThresholdYearsOld,
        monthsOld: maxThresholdMonthsOld,
        retirementAge: {
          age: maxThresholdYearsOld,
          month: maxThresholdMonthsOld,
        },
      }
    }

    //No thresholds limits  have been reached,
    //safely increment the year
    year++

    const { age: yearsOldOnRetirementDate, months: monthsOldOnRetirementDate } =
      calculateRetirementValues(userDetails, {
        year,
        month: value?.month || 0,
      }) || { age: 0, months: 0 }

    newValue = {
      ...value,
      year,
      yearsOld: yearsOldOnRetirementDate,
      monthsOld: monthsOldOnRetirementDate,
      retirementAge: {
        age: yearsOldOnRetirementDate,
        month: monthsOldOnRetirementDate,
      },
    }

    setValue(newValue)
  }

  /**
   * Decrements of the year by one, uses the `onChangeRangeSlider` function to
   * update the the year state
   */
  const handleYearsDecrement = () => onChangeRangeSlider((value?.year || 0) - 1)

  /**
   * Handler when the range thumb is dragged
   */
  const onChangeRangeSlider = (year: number) => {
    let newValue = { ...value }

    const { age: yearsOldOnRetirementDate, months: monthsOldOnRetirementDate } =
      calculateRetirementValues(userDetails, {
        year,
        month: value?.month || 0,
      }) || { age: 0, months: 0 }

    // Max limits reached while dragging the range slider
    if (
      checkMaxLimits(year, maxThresholdYear, value?.month, maxThresholdMonth)
    ) {
      newValue = {
        ...value,
        year: maxThresholdYear,
        month: maxThresholdMonth,
        yearsOld: maxThresholdYearsOld,
        monthsOld: maxThresholdMonthsOld,
        retirementAge: {
          age: maxThresholdYearsOld,
          month: maxThresholdMonthsOld,
        },
      }
    }

    //Min limits reached while dragging the range slider
    if (
      checkMinLimits(year, minThresholdYear, value?.month, minThresholdMonth)
    ) {
      newValue = {
        ...value,
        year: minThresholdYear,
        month: minThresholdMonth,
        yearsOld: minThresholdYearOld,
        monthsOld: minThresholdMonthsOld,
        retirementAge: {
          age: minThresholdYearOld,
          month: minThresholdMonthsOld,
        },
      }
    }

    newValue = {
      ...value,
      year,
      yearsOld: yearsOldOnRetirementDate,
      retirementAge: {
        age: yearsOldOnRetirementDate,
        month: monthsOldOnRetirementDate,
      },
    }

    setValue(newValue)
  }

  return (
    <article className={style['date-slider-box']}>
      <InputLabel label={label} className={style['date-slider-box__label']} />
      <div className={style['date-slider-box__bg']}>
        <DateSliderBox
          year={value?.year || 0}
          month={value?.month || 0}
          onClickIncrementMonths={handleMonthsIncrementByOne}
          onClickDecrementMonths={handleMonthsDecrementByOne}
          onClickIncrementYears={handleYearsIncrement}
          onClickDecrementYears={handleYearsDecrement}
          yearLabel={yearHeadLabel}
          monthLabel={monthHeadLabel}
          locale={locale}
          disabledIncrementYear={(value?.year || 0) >= maxThresholdYear}
          disabledIncrementMonth={maxLimitsReached}
          disabledDecrementYear={(value?.year || 0) <= minThresholdYear}
          disabledDecrementMonth={minLimitsReached}
        >
          <InputLabel
            label={caption}
            className={style['date-slider-box__label']}
          />

          <Range
            steps={sliderSteps}
            onChange={onChangeRangeSlider}
            value={value?.year || 0}
            thumbBubble={
              <MonthAndYearBubble
                year={yearsOldOnRetirementDate || 0}
                month={monthsOldOnRetirementDate || 0}
                yearLabel={yearsOldOnRetirementDateLabel || ''}
                monthLabel={monthsOldOnRetirementDateLabel || ''}
              />
            }
            className={style[`date-slider-box__range`]}
          />
        </DateSliderBox>
      </div>
    </article>
  )
}

/**
 * Checks if the threshold month and year are withing MAX limits
 */
const checkMaxLimits = (
  currentYear: number | undefined,
  thresholdYear: number,
  currentMonth: number | undefined,
  thresholdMonth: number
) =>
  (currentYear || 0) >= thresholdYear && (currentMonth || 0) >= thresholdMonth

/**
 * Checks if the threshold month and year are withing MIN limits
 */
const checkMinLimits = (
  currentYear: number | undefined,
  thresholdYear: number,
  currentMonth: number | undefined,
  thresholdMonth: number
) =>
  (currentYear || 0) <= thresholdYear && (currentMonth || 0) <= thresholdMonth

export default DateSlider
