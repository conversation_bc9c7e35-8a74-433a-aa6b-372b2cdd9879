# THIS ENV FILE REMAINS COMMENTED OUT!
# THIS FILE GETS COMMITTED TO THE REPO WITHOUT API KEYS!

# Copy this file uncomment into your .env.local
# you can build and preview different envs locally by uncommenting
# the values you want to use in YOUR .env.local 

# Locize
# VITE_LOCIZE_PROJECT_ID=
# VITE_LOCIZE_API_KEY=

# # Mixpanel
# VITE_MIX_PANEL_API_KEY=
# VITE_MIX_PANEL_PROXY=https://mixpanel.mytontine.com
# VITE_ANALYTICS_TRACK=false

# App
# LOCAL DEV
# For hot reloading server this should be empty
# do not pass in anything
# VITE_ENVIRONMENT= #change this to "development" for local e2e testing
# VITE_PORT=8080
# VITE_HOST=0.0.0.0
# VITE_BUILD_VARIATION=full
# VITE_INSTRUMENT_CODE=false
# VITE_APP_ENV_COLOR="#f96767"
# VITE_ROBO_BACKEND_URL=http://localhost:8082
# VITE_ROBO_EMAIL_ENV=dev

# STAGING
# VITE_ENVIRONMENT=staging
# VITE_PORT=9000
# VITE_HOST=0.0.0.0
# VITE_BUILD_VARIATION=full
# VITE_INSTRUMENT_CODE=false
# VITE_APP_ENV_COLOR="#f9de67"
# VITE_ROBO_BACKEND_URL=https://staging-api.mytontine.com
# VITE_ROBO_EMAIL_ENV=staging

# # PRODUCTION
# VITE_ENVIRONMENT=production
# VITE_PORT=9000
# VITE_HOST=0.0.0.0
# VITE_BUILD_VARIATION=full
# VITE_INSTRUMENT_CODE=false
# VITE_APP_ENV_COLOR="#67f967"
# VITE_ROBO_BACKEND_URL=https://api.mytontine.com
# VITE_ROBO_EMAIL_ENV=prod

